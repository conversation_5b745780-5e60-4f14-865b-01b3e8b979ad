if(!self.define){let e,s={};const a=(a,n)=>(a=new URL(a+".js",n).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(n,c)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let t={};const r=e=>a(e,i),f={module:{uri:i},exports:t,require:r};s[i]=Promise.all(n.map(e=>f[e]||r(e))).then(e=>(c(...e),t))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"d7e3641097fdc3f2e7e45c6a5c1c2785"},{url:"/_next/static/7p-XD7koCp07cONdNzhSw/_buildManifest.js",revision:"f54e145a63e302d51212d5fcceb86ed6"},{url:"/_next/static/7p-XD7koCp07cONdNzhSw/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1255-ad92d48e3e7ce61a.js",revision:"ad92d48e3e7ce61a"},{url:"/_next/static/chunks/1356-32e9c12aa6fb6d3f.js",revision:"32e9c12aa6fb6d3f"},{url:"/_next/static/chunks/1646.d98ed94ef3a4e400.js",revision:"d98ed94ef3a4e400"},{url:"/_next/static/chunks/2610-2463e72906bf473e.js",revision:"2463e72906bf473e"},{url:"/_next/static/chunks/2619-04bc32f026a0d946.js",revision:"04bc32f026a0d946"},{url:"/_next/static/chunks/3287-ca6534565e0573c6.js",revision:"ca6534565e0573c6"},{url:"/_next/static/chunks/4134-796cf8a1566cb4b2.js",revision:"796cf8a1566cb4b2"},{url:"/_next/static/chunks/4bd1b696-100b9d70ed4e49c1.js",revision:"100b9d70ed4e49c1"},{url:"/_next/static/chunks/5088-cdf4f36f8fd8e3a6.js",revision:"cdf4f36f8fd8e3a6"},{url:"/_next/static/chunks/5139.e4ff9cc3669129ed.js",revision:"e4ff9cc3669129ed"},{url:"/_next/static/chunks/5239-eb77b1a7e712e567.js",revision:"eb77b1a7e712e567"},{url:"/_next/static/chunks/5789-fdc1715b359ff4be.js",revision:"fdc1715b359ff4be"},{url:"/_next/static/chunks/5882-60289a0d0f273cc6.js",revision:"60289a0d0f273cc6"},{url:"/_next/static/chunks/6230-53f2bad6b5946251.js",revision:"53f2bad6b5946251"},{url:"/_next/static/chunks/6604-18983df9b08b916f.js",revision:"18983df9b08b916f"},{url:"/_next/static/chunks/7250-e53f605a0c24927a.js",revision:"e53f605a0c24927a"},{url:"/_next/static/chunks/8652-a0b7b18e83d6149d.js",revision:"a0b7b18e83d6149d"},{url:"/_next/static/chunks/8964-865145a783e97963.js",revision:"865145a783e97963"},{url:"/_next/static/chunks/9972-6552c6c71285a15a.js",revision:"6552c6c71285a15a"},{url:"/_next/static/chunks/app/(site)/about/page-9df5597673ca0c2f.js",revision:"9df5597673ca0c2f"},{url:"/_next/static/chunks/app/(site)/contact/page-b14438dc8417898d.js",revision:"b14438dc8417898d"},{url:"/_next/static/chunks/app/(site)/experience/page-b918f3bf2724f2e4.js",revision:"b918f3bf2724f2e4"},{url:"/_next/static/chunks/app/(site)/layout-ae7ae3e9c78d5df4.js",revision:"ae7ae3e9c78d5df4"},{url:"/_next/static/chunks/app/(site)/page-e1087b596e6ef67e.js",revision:"e1087b596e6ef67e"},{url:"/_next/static/chunks/app/(site)/skills/page-544be5743ba3b9b4.js",revision:"544be5743ba3b9b4"},{url:"/_next/static/chunks/app/_not-found/page-eafb535c88f9a059.js",revision:"eafb535c88f9a059"},{url:"/_next/static/chunks/app/admin/about/page-34e0d689038036d4.js",revision:"34e0d689038036d4"},{url:"/_next/static/chunks/app/admin/contact-settings/page-5176adb186794e44.js",revision:"5176adb186794e44"},{url:"/_next/static/chunks/app/admin/cv-management/page-66970136ee1d339a.js",revision:"66970136ee1d339a"},{url:"/_next/static/chunks/app/admin/dashboard/page-b918f3bf2724f2e4.js",revision:"b918f3bf2724f2e4"},{url:"/_next/static/chunks/app/admin/experience/edit/%5Bid%5D/page-7dc2cfae3b870efa.js",revision:"7dc2cfae3b870efa"},{url:"/_next/static/chunks/app/admin/experience/new/page-b3fdd33e78eb1a6c.js",revision:"b3fdd33e78eb1a6c"},{url:"/_next/static/chunks/app/admin/experience/page-5c538d2c9e01b14f.js",revision:"5c538d2c9e01b14f"},{url:"/_next/static/chunks/app/admin/layout-34f48fe3ab239b24.js",revision:"34f48fe3ab239b24"},{url:"/_next/static/chunks/app/admin/login/page-a8d66a56ebf25b7c.js",revision:"a8d66a56ebf25b7c"},{url:"/_next/static/chunks/app/admin/messages/page-38680e0402ef4931.js",revision:"38680e0402ef4931"},{url:"/_next/static/chunks/app/admin/page-b918f3bf2724f2e4.js",revision:"b918f3bf2724f2e4"},{url:"/_next/static/chunks/app/admin/skills/edit/%5Bid%5D/page-d51cd37205bfea0c.js",revision:"d51cd37205bfea0c"},{url:"/_next/static/chunks/app/admin/skills/new/page-fdc8cdaf49e6e474.js",revision:"fdc8cdaf49e6e474"},{url:"/_next/static/chunks/app/admin/skills/page-548504d61beed5ab.js",revision:"548504d61beed5ab"},{url:"/_next/static/chunks/app/api/auth/login/route-b918f3bf2724f2e4.js",revision:"b918f3bf2724f2e4"},{url:"/_next/static/chunks/app/api/auth/logout/route-b918f3bf2724f2e4.js",revision:"b918f3bf2724f2e4"},{url:"/_next/static/chunks/app/layout-9e0482b3eb2462ff.js",revision:"9e0482b3eb2462ff"},{url:"/_next/static/chunks/b1644e8c-34145c37abc72b1c.js",revision:"34145c37abc72b1c"},{url:"/_next/static/chunks/framework-b9fd9bcc3ecde907.js",revision:"b9fd9bcc3ecde907"},{url:"/_next/static/chunks/main-app-c5e0ee2e35f7940f.js",revision:"c5e0ee2e35f7940f"},{url:"/_next/static/chunks/main-d1de9847cd753580.js",revision:"d1de9847cd753580"},{url:"/_next/static/chunks/pages/_app-e8b861c87f6f033c.js",revision:"e8b861c87f6f033c"},{url:"/_next/static/chunks/pages/_error-3f5d48a2f8729a8f.js",revision:"3f5d48a2f8729a8f"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-8e2654ff42c9155e.js",revision:"8e2654ff42c9155e"},{url:"/_next/static/css/5cfcb17cad615928.css",revision:"5cfcb17cad615928"},{url:"/_next/static/media/028c0d39d2e8f589-s.p.woff2",revision:"c47061a6ce9601b5dea8da0c9e847f79"},{url:"/_next/static/media/5b01f339abf2f1a5.p.woff2",revision:"c36289c8eb40b089247060459534962c"},{url:"/android-chrome-192x192.png",revision:"b8e76b480079cf5a30dba61a52f6a1a1"},{url:"/android-chrome-512x512.png",revision:"e1637c2a8f32d08ac6b965e8de7d0da0"},{url:"/apple-touch-icon.png",revision:"a1ecd594402ffac7334a5f9d2e9b2049"},{url:"/favicon-16x16.png",revision:"71500608c8ec1f8640ead5ed043c6fa0"},{url:"/favicon-32x32.png",revision:"15ddc0450dfbf2ba27acbc78f0f63cf2"},{url:"/manifest.json",revision:"dbb055271fbaa4541650c3ce7a60e183"},{url:"/uploads/cv/1758897740460-CopyofCopyofProfessionCv.pdf",revision:"762ebc43c1fdf64c50e211046b2cd7f1"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:n})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
