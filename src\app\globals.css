@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 240 17% 95%; /* Light desaturated gray #F0F0F5 */
    --foreground: 240 10% 20%; /* Dark gray for text */

    --card: 240 17% 98%; /* Slightly lighter than background for cards */
    --card-foreground: 240 10% 20%;

    --popover: 240 17% 98%;
    --popover-foreground: 240 10% 20%;

    --primary: 283 44% 47%; /* Muted plum #8E44AD - for main buttons */
    --primary-foreground: 283 44% 95%; /* Light text on plum */

    --secondary: 262 100% 90%; /* Lighter Soft lavender for secondary buttons/elements */
    --secondary-foreground: 262 100% 30%; /* Darker lavender text */
    
    --muted: 240 10% 90%; /* Muted gray */
    --muted-foreground: 240 10% 45%;

    --accent: 262 100% 84%; /* Soft lavender #D1B0FF - for highlights, interactive elements */
    --accent-foreground: 262 100% 25%; /* Darker lavender text for contrast on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 10% 85%;
    --input: 240 10% 88%;
    --ring: 283 44% 47%; /* Muted plum for focus rings */
    
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific theme variables, can be adjusted if admin panel needs different styling */
    --sidebar-background: 240 17% 98%; /* Light background for admin sidebar in light mode */
    --sidebar-foreground: 240 10% 20%; /* Dark text for admin sidebar in light mode */
    --sidebar-primary: 283 44% 47%; /* Muted plum for primary elements in sidebar */
    --sidebar-primary-foreground: 283 44% 95%; /* Light text on plum */
    --sidebar-accent: 262 100% 90%; /* Light accent for hover in light mode */
    --sidebar-accent-foreground: 262 100% 30%; /* Dark text on light accent */
    --sidebar-border: 240 10% 85%; /* Light border for sidebar */
    --sidebar-ring: 283 44% 47%; /* Muted plum for focus rings */
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 240 17% 95%; 

    --card: 240 8% 10%; /* Adjusted: Darker card, slightly lighter than pure black */
    --card-foreground: 240 17% 95%;

    --popover: 240 8% 10%; /* Adjusted: Consistent with card */
    --popover-foreground: 240 17% 95%;

    --primary: 262 100% 84%; /* Soft lavender for primary in dark mode */
    --primary-foreground: 262 100% 25%; /* Darker lavender text */

    --secondary: 283 44% 30%; /* Darker plum for secondary */
    --secondary-foreground: 283 44% 90%; 

    --muted: 240 10% 14.9%;
    --muted-foreground: 240 10% 63.9%;

    --accent: 283 44% 47%; /* Muted plum for accent in dark mode */
    --accent-foreground: 283 44% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 10% 14.9%;
    --input: 240 10% 14.9%;
    --ring: 262 100% 84%; /* Soft lavender for rings */
    
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Adjust sidebar for dark mode if necessary, current sidebar vars are already dark-themed */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 262 100% 84%;
    --sidebar-primary-foreground: 262 100% 25%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 262 100% 84%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem; /* Account for navbar height */
  }
  body {
    @apply bg-background text-foreground;
    @apply min-h-screen flex flex-col;
  }
  main {
    @apply flex-grow;
  }
}
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced border utilities */
.border-3 {
  border-width: 3px;
}

/* Custom animations for hero section */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fadeInRight {
  animation: fadeInRight 0.8s ease-out forwards;
}

/* Staggered animation delays */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* Custom responsive utilities for extra large screens */
@media (min-width: 1536px) {
  .text-9xl {
    font-size: 8rem;
    line-height: 1;
  }
  .text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}

.bg-size-200 {
  background-size: 200% 200%;
}

/* Additional responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Ensure text doesn't overflow on small screens */
  h1, h2, h3, h4, h5, h6 {
    word-break: break-word;
    hyphens: auto;
  }

  /* Better spacing for mobile */
  .space-y-8 > * + * {
    margin-top: 1.5rem;
  }

  /* Improve button sizing on mobile */
  .btn-responsive {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  /* Ensure hero content fits properly on mobile */
  section[id="home"] {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* Improve text alignment and spacing */
.text-center {
  text-align: center;
}

/* Better responsive containers */
@media (min-width: 640px) and (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}